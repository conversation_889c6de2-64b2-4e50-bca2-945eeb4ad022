import DriverInfoPanel from '@/components/DriverInfoPanel';
import TripTimer from '@/components/TripTimer';
import { Colors } from '@/constants/Colors';
import { useLocationTracking } from '@/hooks/useLocationTracking';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Dimensions,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
// Conditional import for maps (only on native platforms)
let MapView: any, Marker: any, Polyline: any, PROVIDER_GOOGLE: any;
if (Platform.OS !== 'web') {
  const maps = require('react-native-maps');
  MapView = maps.default;
  Marker = maps.Marker;
  Polyline = maps.Polyline;
  PROVIDER_GOOGLE = maps.PROVIDER_GOOGLE;
}

const { width, height } = Dimensions.get('window');

// Mock data for the trip
const TRIP_DATA = {
  startTime: new Date(),
  estimatedDuration: 40, // 40 minutes
  route: [
    { latitude: 34.0209, longitude: -6.8416 }, // Home
    { latitude: 34.0250, longitude: -6.8350 }, // Bus stop
    { latitude: 34.0300, longitude: -6.8200 }, // University
  ],
  driver: {
    id: '1',
    name: 'أحمد محمد',
    rating: 4.8,
    totalTrips: 156,
    phoneNumber: '+212600123456',
    vehicle: {
      make: 'Toyota',
      model: 'Hiace',
      year: 2020,
      color: 'أبيض',
      licensePlate: 'A-12345-ب',
    },
  },
};

export default function LiveTripScreen() {
  const router = useRouter();
  const {
    currentLocation,
    isTracking,
    hasPermission,
    error,
    startTracking,
    stopTracking,
  } = useLocationTracking();

  const [tripStatus, setTripStatus] = useState<'starting' | 'active' | 'completed'>('starting');
  const [mapReady, setMapReady] = useState(false);

  useEffect(() => {
    // Start location tracking when component mounts
    const initializeTracking = async () => {
      const success = await startTracking();
      if (success) {
        setTripStatus('active');
      }
    };

    initializeTracking();

    // Cleanup on unmount
    return () => {
      stopTracking();
    };
  }, []);

  const handleEndTrip = () => {
    Alert.alert(
      'إنهاء الرحلة',
      'هل أنت متأكد من إنهاء الرحلة؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'إنهاء',
          style: 'destructive',
          onPress: () => {
            stopTracking();
            setTripStatus('completed');
            router.replace('/(tabs)');
          },
        },
      ]
    );
  };

  const handleEmergency = () => {
    Alert.alert(
      'طوارئ',
      'هل تحتاج للمساعدة؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'اتصال بالطوارئ',
          style: 'destructive',
          onPress: () => {
            // In a real app, this would call emergency services
            Alert.alert('تم الاتصال', 'تم إرسال طلب المساعدة');
          },
        },
      ]
    );
  };

  const getMapRegion = () => {
    if (currentLocation) {
      return {
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        latitudeDelta: 0.02,
        longitudeDelta: 0.02,
      };
    }
    
    // Default to trip start location
    return {
      latitude: TRIP_DATA.route[0].latitude,
      longitude: TRIP_DATA.route[0].longitude,
      latitudeDelta: 0.02,
      longitudeDelta: 0.02,
    };
  };

  if (!hasPermission) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>إذن الموقع مطلوب لتتبع الرحلة</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => router.back()}>
          <Text style={styles.retryButtonText}>العودة</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.light.primary} />

      {/* Custom Navigation Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonIcon}>←</Text>
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>الرحلة المباشرة</Text>
          <Text style={styles.headerSubtitle}>
            {tripStatus === 'starting' && 'جاري البدء...'}
            {tripStatus === 'active' && 'قيد التنفيذ'}
            {tripStatus === 'completed' && 'مكتملة'}
          </Text>
        </View>
        
        <TouchableOpacity style={styles.emergencyButton} onPress={handleEmergency}>
          <Text style={styles.emergencyIcon}>🚨</Text>
        </TouchableOpacity>
      </View>

      {/* Map Container */}
      <View style={styles.mapContainer}>
        {Platform.OS === 'web' ? (
          // Web placeholder
          <View style={[styles.map, styles.webMapPlaceholder]}>
            <Text style={styles.webMapText}>🗺️</Text>
            <Text style={styles.webMapSubtext}>Live Trip Map</Text>
            <Text style={styles.webMapNote}>(Maps work on mobile devices)</Text>
          </View>
        ) : (
          // Native map
          <MapView
            style={styles.map}
            provider={PROVIDER_GOOGLE}
            region={getMapRegion()}
            onMapReady={() => setMapReady(true)}
            showsUserLocation={true}
            showsMyLocationButton={false}
            followsUserLocation={true}
            showsCompass={true}
            mapType="standard"
          >
            {/* Route Polyline */}
            <Polyline
              coordinates={TRIP_DATA.route}
              strokeColor={Colors.light.primary}
              strokeWidth={4}
              lineDashPattern={[0]}
            />

            {/* Route Markers */}
            {TRIP_DATA.route.map((location, index) => (
              <Marker
                key={index}
                coordinate={location}
                pinColor={
                  index === 0 ? Colors.light.success :
                  index === TRIP_DATA.route.length - 1 ? Colors.light.error :
                  Colors.light.warning
                }
                title={
                  index === 0 ? 'نقطة البداية' :
                  index === TRIP_DATA.route.length - 1 ? 'الوجهة' :
                  'محطة'
                }
              />
            ))}
          </MapView>
        )}
        
        {/* Map Overlay Controls */}
        <View style={styles.mapOverlay}>
          <View style={styles.statusIndicator}>
            <View style={[styles.statusDot, { 
              backgroundColor: isTracking ? Colors.light.success : Colors.light.warning 
            }]} />
            <Text style={styles.statusText}>
              {isTracking ? 'متصل' : 'غير متصل'}
            </Text>
          </View>
        </View>
      </View>

      {/* Bottom Panel */}
      <View style={styles.bottomPanel}>
        {/* Trip Timer */}
        <TripTimer
          startTime={TRIP_DATA.startTime}
          estimatedDuration={TRIP_DATA.estimatedDuration}
          style={styles.timerSection}
        />

        {/* Driver Info Panel */}
        <DriverInfoPanel
          driver={TRIP_DATA.driver}
          style={styles.driverSection}
        />

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.endTripButton}
            onPress={handleEndTrip}
          >
            <Text style={styles.endTripButtonText}>إنهاء الرحلة</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    padding: 24,
  },
  errorText: {
    fontSize: 18,
    color: Colors.light.error,
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  retryButtonText: {
    color: Colors.light.background,
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    backgroundColor: Colors.light.primary,
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.background + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonIcon: {
    fontSize: 20,
    color: Colors.light.background,
    fontWeight: '600',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.background,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.light.background + 'CC',
    marginTop: 2,
  },
  emergencyButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.error,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emergencyIcon: {
    fontSize: 18,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  mapOverlay: {
    position: 'absolute',
    top: 16,
    right: 16,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.text,
  },
  bottomPanel: {
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    maxHeight: height * 0.6,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  timerSection: {
    marginBottom: 16,
  },
  driverSection: {
    marginBottom: 16,
  },
  actionButtons: {
    marginTop: 8,
  },
  endTripButton: {
    backgroundColor: Colors.light.error,
    paddingVertical: 16,
    borderRadius: 16,
    alignItems: 'center',
  },
  endTripButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.background,
  },
  // Web Map Placeholder Styles
  webMapPlaceholder: {
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#e5e7eb',
    borderStyle: 'dashed',
  },
  webMapText: {
    fontSize: 48,
    marginBottom: 12,
  },
  webMapSubtext: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  webMapNote: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
});
