import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Dimensions,
    Linking,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

// Conditional import for maps (only on native platforms)
let MapView: any, Marker: any, PROVIDER_GOOGLE: any;
if (Platform.OS !== 'web') {
  const maps = require('react-native-maps');
  MapView = maps.default;
  Marker = maps.Marker;
  PROVIDER_GOOGLE = maps.PROVIDER_GOOGLE;
}

const { width, height } = Dimensions.get('window');

// Trip timing data
const TRIP_TIMING = {
  departureTime: '0:42',
  arrivalTime: '00:44',
  remainingTime: '39:17',
  departureLabel: 'الوقت المتوقع',
  arrivalLabel: 'وقت الوصول المتوقع',
  remainingLabel: 'الوقت المتبقي'
};

// Driver information
const DRIVER_INFO = {
  name: 'أحمد محمد',
  rating: 4.8,
  totalTrips: 156,
  avatar: 'أ',
  phone: '+212600123456'
};

// Vehicle information
const VEHICLE_INFO = {
  model: '2020 Toyota Hiace',
  color: 'أبيض',
  plateNumber: 'ب-12345',
  type: 'النوع:',
  colorLabel: 'اللون:',
  plateLabel: 'رقم اللوحة:'
};

export default function LiveTripDetailsScreen() {
  const router = useRouter();
  const [currentLocation] = useState({
    latitude: 34.0209,
    longitude: -6.8416,
  });

  const handleCallDriver = () => {
    Alert.alert("اتصال بالسائق", `هل تريد الاتصال بـ ${DRIVER_INFO.name}؟`, [
      { text: "إلغاء", style: "cancel" },
      {
        text: "اتصال",
        onPress: () => {
          Linking.openURL(`tel:${DRIVER_INFO.phone}`)
        },
      },
    ]);
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Text key={i} style={[styles.star, { color: i <= rating ? '#FFD700' : '#E5E7EB' }]}>
          ⭐
        </Text>
      );
    }
    return stars;
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#4F46E5" translucent />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>الرحلة المباشرة</Text>
          <Text style={styles.headerSubtitle}>قيد التنفيذ</Text>
        </View>
        <TouchableOpacity style={styles.emergencyButton}>
          <Text style={styles.emergencyIcon}>🚨</Text>
        </TouchableOpacity>
      </View>

      {/* Map Section */}
      <View style={styles.mapContainer}>
        {Platform.OS === 'web' ? (
          // Web placeholder
          <View style={[styles.map, styles.webMapPlaceholder]}>
            <Text style={styles.webMapText}>🗺️</Text>
            <Text style={styles.webMapSubtext}>Live Trip Map</Text>
            <Text style={styles.webMapNote}>(Maps work on mobile devices)</Text>
          </View>
        ) : (
          // Native map
          <MapView
            style={styles.map}
            provider={PROVIDER_GOOGLE}
            initialRegion={{
              latitude: currentLocation.latitude,
              longitude: currentLocation.longitude,
              latitudeDelta: 0.02,
              longitudeDelta: 0.02,
            }}
            showsUserLocation={true}
            showsMyLocationButton={false}
          >
            <Marker
              coordinate={currentLocation}
              title="موقعك الحالي"
              pinColor="#4F46E5"
            />
          </MapView>
        )}
        
        {/* Connected indicator */}
        <View style={styles.connectionIndicator}>
          <View style={styles.connectionDot} />
          <Text style={styles.connectionText}>متصل</Text>
        </View>
      </View>

      {/* Trip Timing Section */}
      <View style={styles.timingSection}>
        <Text style={styles.sectionTitle}>موقت الرحلة ⏱️</Text>
        <View style={styles.timingContainer}>
          <View style={styles.timingItem}>
            <View style={styles.timingIcon}>
              <Text style={styles.iconText}>🕐</Text>
            </View>
            <Text style={styles.timingValue}>{TRIP_TIMING.departureTime}</Text>
            <Text style={styles.timingLabel}>{TRIP_TIMING.departureLabel}</Text>
          </View>
          
          <View style={styles.timingItem}>
            <View style={styles.timingIcon}>
              <Text style={styles.iconText}>🎯</Text>
            </View>
            <Text style={styles.timingValue}>{TRIP_TIMING.arrivalTime}</Text>
            <Text style={styles.timingLabel}>{TRIP_TIMING.arrivalLabel}</Text>
          </View>
          
          <View style={styles.timingItem}>
            <View style={styles.timingIcon}>
              <Text style={styles.iconText}>⏳</Text>
            </View>
            <Text style={styles.timingValue}>{TRIP_TIMING.remainingTime}</Text>
            <Text style={styles.timingLabel}>{TRIP_TIMING.remainingLabel}</Text>
          </View>
        </View>
      </View>

      {/* Driver Information Section */}
      <View style={styles.driverSection}>
        <Text style={styles.sectionTitle}>معلومات السائق</Text>
        <View style={styles.driverCard}>
          <View style={styles.driverInfo}>
            <View style={styles.driverAvatar}>
              <Text style={styles.driverAvatarText}>{DRIVER_INFO.avatar}</Text>
              <View style={styles.onlineIndicator} />
            </View>
            <View style={styles.driverDetails}>
              <Text style={styles.driverName}>{DRIVER_INFO.name}</Text>
              <View style={styles.ratingContainer}>
                <View style={styles.starsContainer}>
                  {renderStars(DRIVER_INFO.rating)}
                </View>
                <Text style={styles.ratingText}>
                  {DRIVER_INFO.rating} ({DRIVER_INFO.totalTrips} رحلة)
                </Text>
              </View>
            </View>
          </View>
          <TouchableOpacity style={styles.callButton} onPress={handleCallDriver}>
            <Text style={styles.callButtonText}>📞</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Vehicle Information Section */}
      <View style={styles.vehicleSection}>
        <Text style={styles.sectionTitle}>معلومات المركبة 🚐</Text>
        <View style={styles.vehicleCard}>
          <View style={styles.vehicleRow}>
            <Text style={styles.vehicleLabel}>{VEHICLE_INFO.type}</Text>
            <Text style={styles.vehicleValue}>{VEHICLE_INFO.model}</Text>
          </View>
          <View style={styles.vehicleRow}>
            <Text style={styles.vehicleLabel}>{VEHICLE_INFO.colorLabel}</Text>
            <Text style={styles.vehicleValue}>{VEHICLE_INFO.color}</Text>
          </View>
          <View style={styles.vehicleRow}>
            <Text style={styles.vehicleLabel}>{VEHICLE_INFO.plateLabel}</Text>
            <Text style={styles.vehiclePlate}>{VEHICLE_INFO.plateNumber}</Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    backgroundColor: '#4F46E5',
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backIcon: {
    fontSize: 20,
    color: 'white',
    fontWeight: 'bold',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
  emergencyButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EF4444',
    alignItems: 'center',
    justifyContent: 'center',
  },
  emergencyIcon: {
    fontSize: 18,
  },
  mapContainer: {
    height: height * 0.35,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  webMapPlaceholder: {
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  webMapText: {
    fontSize: 48,
    marginBottom: 12,
  },
  webMapSubtext: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  webMapNote: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  connectionIndicator: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: 'white',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  connectionDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#10B981',
    marginRight: 6,
  },
  connectionText: {
    fontSize: 12,
    color: '#374151',
    fontWeight: '600',
  },
  // Timing Section Styles
  timingSection: {
    backgroundColor: 'white',
    margin: 16,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  timingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  timingItem: {
    alignItems: 'center',
    flex: 1,
  },
  timingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  iconText: {
    fontSize: 18,
  },
  timingValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#4F46E5',
    marginBottom: 4,
  },
  timingLabel: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  // Driver Section Styles
  driverSection: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  driverCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  driverInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  driverAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#4F46E5',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    position: 'relative',
  },
  driverAvatarText: {
    fontSize: 24,
    fontWeight: '700',
    color: 'white',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#10B981',
    borderWidth: 2,
    borderColor: 'white',
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'right',
    marginBottom: 8,
  },
  ratingContainer: {
    alignItems: 'flex-end',
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  star: {
    fontSize: 16,
    marginHorizontal: 1,
  },
  ratingText: {
    fontSize: 14,
    color: '#6B7280',
  },
  callButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#10B981',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  callButtonText: {
    fontSize: 20,
    color: 'white',
  },
  // Vehicle Section Styles
  vehicleSection: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  vehicleCard: {
    gap: 12,
  },
  vehicleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  vehicleLabel: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  vehicleValue: {
    fontSize: 14,
    color: '#1F2937',
    fontWeight: '600',
  },
  vehiclePlate: {
    fontSize: 14,
    color: '#4F46E5',
    fontWeight: '700',
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
});
